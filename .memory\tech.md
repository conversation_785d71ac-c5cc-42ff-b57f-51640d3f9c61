# Teknologi & Setup

## Tech Stack
- **PHP**: 7.4.33
- **<PERSON><PERSON>**: 8.83.29
- **Template**: Sneat Bootstrap Admin Template (Free Version)
- **Frontend**: Bootstrap 5, jQuery, ApexCharts
- **Icons**: Boxicons, Iconify

## Dependencies & Assets
### CSS Dependencies
- `assets/vendor/css/core.css` - Core Sneat styles
- `assets/css/demo.css` - Demo styles
- `assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css`
- `assets/vendor/fonts/iconify-icons.css`

### JavaScript Dependencies
- `assets/vendor/libs/jquery/jquery.js`
- `assets/vendor/libs/popper/popper.js`
- `assets/vendor/js/bootstrap.js`
- `assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js`
- `assets/vendor/js/menu.js`
- `assets/js/main.js`

### Optional Libraries (per halaman)
- ApexCharts untuk dashboard analytics
- Perfect Scrollbar untuk custom scrollbars

## Setup Instructions

### 1. Requirements
- PHP 7.4.33 atau lebih tinggi
- Composer
- Web server (Apache/Nginx) atau Laravel development server

### 2. Installation
```bash
# Clone atau download proyek
cd regkan

# Install dependencies
composer install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Start development server
php artisan serve
```

### 3. Struktur URL
- `/` atau `/dashboard` - Dashboard utama
- `/cards` - Halaman contoh cards

## Constraints & Compatibility
- **PHP Version**: Minimum 7.4.33, tested dengan 7.4.33
- **Laravel Version**: 8.x (kompatibel dengan PHP 7.4)
- **Browser Support**: Modern browsers (Chrome, Firefox, Safari, Edge)
- **Responsive**: Mobile-first design, support semua device sizes

## Performance Considerations
- Assets di-serve langsung dari `public/assets/`
- CSS dan JS files sudah di-minify dari template original
- Images optimized untuk web
- Lazy loading untuk components yang tidak critical

## Security Features
- CSRF protection enabled
- XSS protection melalui Blade templating
- Secure asset serving
- Environment-based configuration
