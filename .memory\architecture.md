# Arsitektur Sistem - Template Sneat Laravel

## Struktur Proyek
```
regkan/
├── app/
│   └── Http/Controllers/
│       ├── DashboardController.php
│       └── CardsController.php
├── public/
│   └── assets/ (Template Sneat assets)
│       ├── css/
│       ├── img/
│       ├── js/
│       └── vendor/
├── resources/views/
│   ├── layouts/
│   │   ├── master.blade.php (Master layout)
│   │   └── partials/
│   │       ├── head.blade.php
│   │       ├── sidebar.blade.php
│   │       ├── navbar.blade.php
│   │       ├── footer.blade.php
│   │       └── scripts.blade.php
│   ├── dashboard/
│   │   └── index.blade.php
│   └── cards/
│       └── index.blade.php
└── routes/
    └── web.php
```

## Arsitektur Layout Modular

### 1. Master Layout (`master.blade.php`)
- Base template yang diextend oleh semua halaman
- Menggunakan struktur HTML dari template Sneat original
- Include semua komponen partial
- Menyediakan section untuk content dan customization

### 2. Komponen Partial
- **Head**: Meta tags, CSS links, fonts, favicon
- **Sidebar**: Navigation menu dengan active state detection
- **Navbar**: Top navigation bar dengan search dan user dropdown
- **Footer**: Footer section dengan links
- **Scripts**: JavaScript files dan dependencies

### 3. Content Pages
- Extend master layout menggunakan `@extends('layouts.master')`
- Override sections sesuai kebutuhan (title, content, scripts, etc.)
- Dapat menambahkan menu items melalui `@section('sidebar-menu')`

## Fitur Arsitektur

### Modular Design
- Setiap komponen layout terpisah untuk maintenance mudah
- Reusable components untuk konsistensi
- Easy customization per halaman

### Asset Management
- Assets template Sneat disimpan di `public/assets/`
- Menggunakan Laravel `asset()` helper untuk path management
- Semua CSS dan JS dependencies ter-organize dengan baik

### Responsive & Interactive
- Mempertahankan semua fitur responsive dari template original
- JavaScript functionality tetap berfungsi (menu toggle, dropdowns, etc.)
- Bootstrap 5 integration

### Extensibility
- Mudah menambahkan halaman baru
- Sidebar menu dapat dikustomisasi per halaman
- Support untuk page-specific CSS dan JavaScript
