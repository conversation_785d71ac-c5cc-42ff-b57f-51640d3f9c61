/* Toasts
******************************************************************************* */

.bs-toast[class^="bg-"],
.bs-toast[class*=" bg-"] {
  --#{$prefix}toast-header-color: var(--#{$prefix}white);
  --#{$prefix}toast-color: var(--#{$prefix}white);
}

.toast.bs-toast {
  --#{$prefix}toast-btn-close-bg: var(--#{$prefix}paper-bg);
  --#{$prefix}toast-bg: rgba(var(--#{$prefix}white-rgb), .85);
  --#{$prefix}toast-btn-close: #{$btn-close-gray};
  z-index: $zindex-toast;
  background-color: var(--#{$prefix}toast-bg) !important;
  .toast-header {
    position: relative;
    padding-block-end: .5rem;
    .btn-close {
      position: absolute;
      padding: .45rem;
      background-color: var(--#{$prefix}toast-btn-close-bg);
      background-image: var(--#{$prefix}toast-btn-close);
      background-size: $toast-btn-close-size;
      box-shadow: var(--#{$prefix}toast-box-shadow);
      filter: none;
      inset-block-start: -8px;
      inset-inline-end: 2px;
      @include border-radius($border-radius);
      @include transition(all .23s ease .1s);

      /* For hover effect of close btn */
      &:hover,
      &:focus,
      &:active {
        opacity: 1;
        outline: 0;
      }
    }
  }
  .toast-header ~ .toast-body {
    padding-block-start: 0;
  }
}
.toast-container {
  --#{$prefix}toast-zindex: 8;
}

.toast-header {
  .btn-close {
    margin-inline: $toast-padding-x $toast-padding-x * -.5;
  }
}

/* Placement Toast example */
.toast-placement-ex {
  position: fixed;
}

/* Generate contextual modifier classes for colorizing the alert */
@each $state in map-keys($theme-colors) {
  .bs-toast{
    &.bg-#{$state} {
      --#{$prefix}toast-bg: rgba(var(--#{$prefix}#{$state}-rgb), .85);
      --#{$prefix}toast-box-shadow: 0 .25rem 1rem rgba(var(--#{$prefix}#{$state}-rgb), .4);
      --#{$prefix}toast-btn-close-bg: var(--#{$prefix}#{$state});
      --#{$prefix}toast-btn-close: #{$btn-close-white};
    }
  }
}
