/* Progress
******************************************************************************* */

.progress {
  --#{$prefix}progress-bar-shadow-color: #{$progress-bar-shadow-color};
  overflow: initial;
  .progress-bar {
    background-color: var(--#{$prefix}primary);
    box-shadow: 0 2px 4px 0 var(--#{$prefix}progress-bar-shadow-color);
    color: var(--#{$prefix}white);
  }

  .progress-bar:first-child {
    @include border-start-radius($progress-border-radius);
  }
  .progress-bar:last-child {
    @include border-end-radius($progress-border-radius);
  }
}

@each $state in map-keys($theme-colors) {
  .progress-bar.bg-#{$state} {
    --#{$prefix}progress-bar-shadow-color: rgba(var(--#{$prefix}#{$state}-rgb), .4);
  }
}
