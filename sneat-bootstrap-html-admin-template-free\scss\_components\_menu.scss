/* Menu
******************************************************************************* */

.menu {
  display: flex;
  background-color: var(--#{$prefix}menu-bg);
  @include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
    box-shadow: var(--#{$prefix}menu-box-shadow);
  }

  .app-brand {
    inline-size: 100%;
    padding-inline: calc(calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} * 2.1333));
    .app-brand-text {
      color: var(--#{$prefix}heading-color);
    }
    .layout-menu-toggle {
      position: absolute;
      z-index: 3;
      border: 7px solid var(--#{$prefix}paper-bg);
      @include border-radius(50%);
      background-color: var(--#{$prefix}primary);
      inset-inline-start: $menu-width - 1.05rem;
      opacity: 1;

      /* transition: opacity $menu-animation-duration ease-in-out; */
      i {
        block-size: 1.375rem;
        color: var(--#{$prefix}white);
        inline-size: 1.375rem;
        line-height: 1;
        transition: all $menu-animation-duration ease-in-out;
      }
      @include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
        &::before {
          position: absolute;
          z-index: -1;
          display: none;
          border: 1px solid var(--#{$prefix}border-color);
          @include border-radius(50%);
          block-size: 2.25rem;
          clip-path: circle(71% at 0% 50%);
          content: "";
          inline-size: 2.25rem;
          inset-block-start: -.46rem;
          inset-inline-start: -.5rem;
        }
      }
      @include media-breakpoint-down(xl) {
        display: none;
        border: 7px solid var(--#{$prefix}paper-bg);
        .layout-menu-expanded & {
          display: block;
        }
      }
    }
  }

  // Sub menu item link bullet
  .menu-sub > .menu-item > .menu-link {
    &::before {
      position: absolute;
      @include border-radius(50%);
      background-color: var(--#{$prefix}gray-400);
      block-size: .375rem;
      content: "";
      inline-size: .375rem;
      inset-inline-start: 1.4375rem;
      .layout-horizontal & {
        inset-inline-start: 1rem;
        @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
          inset-inline-start: 1.5rem;
        }
      }
    }
  }

  // ? Hide bullet from first child only as we display icon instead (horizontal)
  &.menu-horizontal .menu-inner > .menu-item > .menu-sub > .menu-item > .menu-link{
    padding-inline-start: $menu-horizontal-link-padding-x;
    &::before {
      display: none;
    }
  }
  &.menu-horizontal .menu-sub .menu-item .menu-link {
    padding-inline-start: $menu-horizontal-link-padding-x + $menu-horizontal-menu-level-spacer;
  }

  /* PS Scrollbar */
  .ps {
    &.ps--active-y > .ps__rail-y {
      background: none;
    }
    .ps__rail-y {
      inset-inline: auto .25rem !important;
    }
    .ps__thumb-y,
    .ps__rail-y {
      inline-size: .125rem;
    }

    .ps__rail-y:hover,
    .ps__rail-y:focus,
    .ps__rail-y.ps--clicking,
    .ps__rail-y:hover > .ps__thumb-y,
    .ps__rail-y:focus > .ps__thumb-y,
    .ps__rail-y.ps--clicking > .ps__thumb-y {
      inline-size: .375rem;
    }

    .ps__thumb-y,
    .ps__rail-y.ps--clicking > .ps__thumb-y {
      opacity: .3;
    }
  }
}

/*  Menu link */
.menu-link {
  position: relative;
  display: flex;
  flex: 0 1 auto;
  align-items: center;
  margin: 0;

  /* link hover animation */
  .menu:not(.menu-no-animation) & {
    transition-duration: $menu-animation-duration;
    transition-property: color, background-color, inset-inline-start;
  }
  .menu-item.disabled & {
    cursor: not-allowed;
  }

  > :not(.menu-icon) {
    flex: 0 1 auto;
    opacity: 1;
    .menu:not(.menu-no-animation) & {
      transition: opacity $menu-animation-duration ease-in-out;
    }
  }
}

.menu-inner {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0;
  margin: 0;
  block-size: 100%;

  > .menu-item.menu-item-closing .menu-item.open .menu-sub,
  > .menu-item.menu-item-closing .menu-item.open .menu-toggle {
    background-color: transparent;
    color: var(--#{$prefix}menu-color);
  }
}

.menu-inner-shadow {
  position: absolute;
  z-index: 2;
  display: none;
  background: linear-gradient(var(--#{$prefix}menu-bg) 41%, rgba(var(--#{$prefix}menu-bg-rgb), .11) 95%, rgba(var(--#{$prefix}menu-bg-rgb), 0));
  inline-size: 100%;
  inset-block-start: $navbar-height + .35rem;
  pointer-events: none;
  @include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
    block-size: 3rem;
  }
  @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
    block-size: 1.5rem;
  }
  .layout-navbar-full & {
    inset-block-start: 0;
  }
}

/* Menu item */
.menu-item {
  align-items: flex-start;
  justify-content: flex-start;

  &.menu-item-animating {
    transition: block-size $menu-animation-duration ease-in-out;
  }
}

/* Horizontal Menu
****************************************************************************** */
.menu.menu-horizontal {

  .menu-inner > .menu-item > .menu-link {
    @include border-radius($border-radius);
  }

  .menu-inner > .menu-item.active > .menu-link.menu-toggle{
    background: var(--#{$prefix}menu-active-bg);
    color: var(--#{$prefix}menu-active-color);
  }

  .menu-inner > .menu-item{
    margin-block: $menu-horizontal-link-padding-y;
    margin-inline: 0;
    &:not(:first-child) {
      margin-inline-start: calc($menu-item-spacer + .0625rem);
    }
    &:not(:last-child) {
      margin-inline-end: calc($menu-item-spacer + .0625rem);
    }
    .menu-sub {
      z-index: 1;
      box-shadow: var(--#{$prefix}menu-horizontal-menu-box-shadow);
    }
  }
}

.menu-item,
.menu-header,
.menu-divider,
.menu-block {
  flex: 0 0 auto;
  flex-direction: column;
  padding: 0;
  margin: 0;
  list-style: none;
}
.menu-header {
  opacity: 1;
  transition: opacity $menu-animation-duration ease-in-out;
  .menu-header-text {
    color: var(--#{$prefix}gray-400);
    letter-spacing: .4px;
    text-transform: uppercase;
    white-space: nowrap;
  }
}

/* Menu Icon */
.menu-icon {
  flex-grow: 0;
  flex-shrink: 0;
  block-size: $menu-icon-expanded-font-size;
  font-size: $menu-icon-expanded-font-size;
  inline-size: $menu-icon-expanded-font-size;
  margin-inline-end: $menu-icon-expanded-spacer;
  .menu:not(.menu-no-animation) & {
    transition: margin-inline-end $menu-animation-duration ease;
  }

  .menu-link {
    transition-duration: $menu-animation-duration;
  }
  .menu-toggle::after {
    transition-duration: $menu-animation-duration;
    transition-property: -webkit-transform, transform;
  }
}

.menu-link,
.menu-horizontal-prev,
.menu-horizontal-next {
  color: var(--#{$prefix}menu-color);

  &:hover,
  &:focus {
    color: var(--#{$prefix}menu-hover-color);
  }
}

.menu-item.disabled .menu-link,
.menu-horizontal-prev.disabled,
.menu-horizontal-next.disabled {
  opacity: .6;
}

/* Sub menu */
.menu-sub {
  display: none;
  flex-direction: column;
  padding: 0;
  margin: 0;

  .menu-item.open > & {
    display: flex;
  }
  > .menu-item {
    &.active {
      > .menu-link {
        background-color: transparent;
        color: var(--#{$prefix}heading-color);
        &:not(.menu-toggle)::before {
          box-shadow: 0 0 0 1px var(--#{$prefix}paper-bg);
        }
        &.menu-toggle {
          background-color: var(--#{$prefix}menu-active-toggle-bg);
          color: var(--#{$prefix}menu-color);
        }
      }
    }
  }
}

/* Menu toggle open/close arrow */
.menu-toggle::after {
  position: absolute;
  display: block;
  border: 2px solid;
  block-size: .45em;
  border-block-end: 0;
  border-inline-start: 0;
  content: "";
  inline-size: .45em;
  inset-block-start: 49%;
  transform: translateY(-50%) rotate(45deg);
}

/* Menu divider */
.menu-divider {
  border: 0;
  border-block-start: 1px solid;
  border-block-start-color: var(--#{$prefix}menu-divider-color);
  inline-size: 100%;
}


// Vertical Menu
// ******************************************************************************

.menu-vertical {
  // overflow: hidden;
  flex-direction: column;

  // menu expand collapse animation
  &:not(.menu-no-animation) {
    transition: inline-size $menu-animation-duration;
  }

  &,
  .menu-block,
  .menu-inner > .menu-item,
  .menu-inner > .menu-header {
    inline-size: var(--#{$prefix}menu-width);
  }

  .menu-inner {
    flex: 1 1 auto;
    flex-direction: column;

    > .menu-item {
      margin-block: var(--#{$prefix}menu-item-spacer);
      margin-inline: 0;
      &.active {
        &::before {
          .layout-wrapper:not(.layout-horizontal) & {
            position: absolute;
            background-color: var(--#{$prefix}primary);
            block-size: 2.6845rem;
            content: "";
            inline-size: .25rem;
            inset-inline-end: 0;
            @include border-top-start-radius($border-radius);
            @include border-bottom-start-radius($border-radius);
          }
        }
        > .menu-link:not(.menu-toggle) {
          background-color: var(--#{$prefix}menu-active-bg);
          color: var(--#{$prefix}menu-active-color);
        }
      }

      /* menu-link spacing */
      .menu-link {
        @include border-radius($border-radius);
        margin-block: 0;
        margin-inline: $menu-vertical-link-margin-x;
      }
    }
  }

  .menu-item .menu-link,
  .menu-block {
    padding-block: var(--#{$prefix}menu-vertical-link-padding-y);
    padding-inline: var(--#{$prefix}menu-vertical-link-padding-x);
  }

  .layout-menu-hover & {
    .menu-inner > .menu-item > .menu-link,
    .menu-inner > .menu-block,
    .menu-inner > .menu-header {
      padding-inline: calc(#{$menu-vertical-link-padding-x} - 1px) calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} + #{$caret-width * 3.2});
    }
  }

  .menu-header {
    position: relative;
    margin-block: $menu-vertical-header-margin-y $menu-vertical-header-margin-y * .5;
    margin-inline: 0;
    padding-block: calc($menu-vertical-link-padding-y * 2);
    padding-inline: calc($menu-vertical-link-padding-x * 2.133);
    &::before {
      position: absolute;
      background-color: var(--#{$prefix}gray-200);
      block-size: 1px;
      content: "";
      inline-size: 1rem;
      inset-block-start: 50%;
      inset-inline-start: 0;
      transition: all $menu-animation-duration ease-in-out;
    }
  }
  .menu-item .menu-link {
    font-size: $menu-font-size;
    min-block-size: 2.625rem;
    > div:not(.badge) {
      overflow: hidden;
      line-height: 1.375rem;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .menu-item {
    .menu-toggle {
      padding-inline-end: calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} + #{$caret-width * 3.2});

      &::after {
        inset-inline-end: calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} + .3rem);
        transition: transform $menu-animation-duration;
      }
    }
    &:not(.active):not(.open) .menu-link:hover {
      background-color: var(--#{$prefix}menu-hover-bg);
    }

    &.active > .menu-toggle {
      background-color: var(--#{$prefix}menu-sub-active-bg);
      color: var(--#{$prefix}menu-sub-active-color);
    }
    &.active:not(.open) > .menu-link:not(.menu-toggle)::before {
      /* border: 3px solid var(--#{$prefix}primary-bg-subtle); */
      background-color: var(--#{$prefix}primary);
      block-size: .5rem;
      box-shadow: 0 0 0 3px var(--#{$prefix}primary-bg-subtle);
      inline-size: .5rem;
      inset-inline-start: 1.4rem;
      .layout-horizontal & {
        inset-inline-start: 1.5rem;
      }
    }
  }

  .menu-item.open:not(.menu-item-closing) > .menu-link::after {
    transform: translateY(-50%) rotate(135deg);
  }

  .menu-divider {
    padding: 0;
    margin-block: $menu-link-spacer-x;
  }

  .menu-sub {
    .menu-item {
      margin-block: calc(var(--#{$prefix}menu-item-spacer) * 2) 0;
      margin-inline: 0;
    }
    .menu-icon {
      .layout-horizontal & {
        @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
          display: none;
        }
      }
    }
  }

  .layout-horizontal & {
    @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
      box-shadow: none;
    }
    .menu-item {
      margin-block-start: $menu-item-spacer * 2;
    }
  }

  .menu-horizontal-wrapper {
    flex: none;
  }

  ~ .menu-mobile-toggler{
    display: none;
    .layout-navbar-hidden & {
      position: fixed;
      z-index: 1067;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--#{$prefix}secondary);
      inset-block-end: calc(#{$container-padding-x} * 2);
      inset-inline-start: $container-padding-x;
    }
  }

  // Levels
  $menu-first-level-spacer: $menu-vertical-link-padding-x + $menu-icon-expanded-width + $menu-icon-expanded-spacer;

  .menu-sub .menu-link {
    padding-inline-start: $menu-first-level-spacer;
  }

  // Menu levels loop for padding left/right
  @for $i from 2 through $menu-max-levels {
    $selector: "";

    @for $l from 1 through $i {
      $selector: "#{$selector} .menu-sub";
    }
    .layout-wrapper:not(.layout-horizontal) & {
      .menu-inner > .menu-item {
        #{$selector} {
          .menu-link {
            padding-inline-start: $menu-first-level-spacer + ($menu-vertical-menu-level-spacer * ($i)) - .8;
            &::before {
              inset-inline-start: $menu-icon-expanded-left-spacer + ($menu-vertical-menu-level-spacer * $i) - 1;
            }
          }
          & > .menu-item.active > {
            .menu-link {
              &::before {
                inset-inline-start: $menu-icon-expanded-left-spacer + ($menu-vertical-menu-level-spacer * $i) - 1.01;
              }
            }
          }
        }
      }
    }
  }
}

/* Vertical Menu Collapsed
******************************************************************************* */

@mixin layout-menu-collapsed() {
  inline-size: var(--#{$prefix}menu-collapsed-width);

  .menu-inner > .menu-item {
    inline-size: var(--#{$prefix}menu-collapsed-width);
  }

  .menu-inner > .menu-header,
  .menu-block {
    position: relative;
    inline-size: var(--#{$prefix}menu-width);
    margin-inline-start: var(--#{$prefix}menu-collapsed-width);
    padding-inline: $menu-icon-expanded-spacer calc(calc(var(--#{$prefix}menu-vertical-link-padding-x) * 2) - $menu-icon-expanded-spacer);
    text-indent: -9999px;
    text-overflow: ellipsis;
    white-space: nowrap;
    .menu-header-text {
      overflow: hidden;
      opacity: 0;
    }

    &::before {
      position: absolute;
      display: block;
      content: "";
      inline-size: calc(var(--#{$prefix}menu-collapsed-width) * .22);
      inset-block: 1.1875rem;
      inset-inline-start: calc(-1 * calc(var(--#{$prefix}menu-collapsed-width) * .61));
      text-align: center;
    }
  }

  /* Custom for sneat only */
  .menu-block {
    &::before {
      block-size: .125rem;
    }
  }
  .menu-inner > .menu-item div:not(.menu-block) {
    overflow: hidden;
    opacity: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .menu-inner > .menu-item > .menu-sub,
  .menu-inner > .menu-item.open > .menu-sub {
    display: none;
  }
  .menu-inner > .menu-item > .menu-toggle::after {
    display: none;
  }

  &:not(.layout-menu-hover) {
    .menu-inner > .menu-item > .menu-link,
    .menu-inner > .menu-block,
    .menu-inner > .menu-header {
      padding-inline: calc(#{$menu-vertical-link-padding-x} - 1px);
    }
  }

  .menu-inner > .menu-item > .menu-link .menu-icon {
    margin-inline-end: 0;
    text-align: center;
  }
}

/* Only for menu example */
.menu-collapsed:not(:hover) {
  @include layout-menu-collapsed();
}

/* Horizontal
******************************************************************************* */

.menu-horizontal {
  flex-direction: row;
  inline-size: 100%;

  .menu-inner {
    overflow: hidden;
    flex: 0 1 100%;
    flex-direction: row;

    .menu-item {
      &:not(.menu-item-closing) > .menu-sub {
        background-color: var(--#{$prefix}menu-bg);
      }
      &:hover > .menu-link {
        background-color: var(--#{$prefix}menu-hover-bg);
        color: var(--#{$prefix}menu-hover-color);
      }
    }
  }

  .menu-item .menu-link {
    padding-block: $menu-horizontal-link-padding-y;
    padding-inline: $menu-horizontal-link-padding-x;
  }

  .menu-item.active > .menu-link:not(.menu-toggle) {
    background: var(--#{$prefix}menu-horizontal-active-bg);
    color: var(--#{$prefix}primary);
    &::before{
      background-color: var(--#{$prefix}primary);
      box-shadow: 0 0 0 3px var(--#{$prefix}primary-bg-subtle);
    }
  }

  .menu-item .menu-toggle {
    padding-inline-end: calc(#{$menu-horizontal-link-padding-x} + #{$caret-width * 3.2});

    &::after {
      inset-block-start: 47%;
      inset-inline-end: calc(#{$menu-horizontal-link-padding-x} + #{.15rem});
    }
  }

  .menu-inner > .menu-item > .menu-toggle {
    &::after {
      transform: translateY(-50%) rotate(135deg);
    }
    &::before {
      position: absolute;
      z-index: 2;
      block-size: $menu-horizontal-link-padding-y;
      content: "";
      inline-size: 100%;
      inset-block-start: 100%;
      inset-inline-start: 0;
      pointer-events: auto;
    }
  }
  .menu-inner > .menu-item > .menu-sub {
    margin-block-start: $menu-horizontal-link-padding-y;
    .menu-sub {
      margin-block: 0;
      margin-inline: $menu-horizontal-spacer-x;
    }
  }
  .menu-inner > .menu-item:not(.menu-item-closing).open .menu-item.open {
    position: relative;
  }

  .menu-sub {
    position: absolute;
    inline-size: $menu-sub-width;
    padding-block: $menu-horizontal-item-spacer;
    padding-inline: 0;
    .menu-item {
      /* padding-inline: $menu-link-spacer-x; */
      &:not(:last-child) {
        margin-block-end: .125rem;
      }
      &.open .menu-link > div::after {
        position: absolute;
        z-index: 2;
        block-size: 100%;
        content: "";
        inline-size: 1.0625rem;
        inset-inline-end: -1.0625rem;
        pointer-events: auto;
      }
    }

    .menu-sub {
      position: absolute;
      inline-size: 100%;
      inset-block-start: 0;
      inset-inline-start: 100%;
    }

    .menu-link {
      padding-block: $menu-horizontal-menu-link-padding-y;
    }
  }

  .menu-inner > .menu-item {
    .menu-sub {
      @include border-radius($border-radius);
      .menu-sub {
        .menu-link {
          padding-inline-start: $menu-horizontal-menu-level-spacer;
        }
      }
    }
  }

  &:not(.menu-no-animation) .menu-inner .menu-item.open .menu-sub {
    animation: menuDropdownShow $menu-animation-duration ease-in-out;
  }

  @include media-breakpoint-down(lg) {
    & {
      display: none;
    }
  }
}

.menu-horizontal-wrapper {
  overflow: hidden;
  flex: 0 1 100%;
  inline-size: 0;

  .menu:not(.menu-no-animation) & .menu-inner {
    transition: margin $menu-animation-duration;
  }
}

.menu-horizontal-prev,
.menu-horizontal-next {
  position: relative;
  display: block;
  flex: 0 0 auto;
  inline-size: $menu-control-width;

  &::after {
    position: absolute;
    display: block;
    border: 1px solid;
    block-size: $menu-control-arrow-size;
    border-block-start: 0;
    content: "";
    inline-size: $menu-control-arrow-size;
    inset-block-start: 50%;
    inset-inline-start: 50%;
  }

  &.disabled {
    cursor: not-allowed;
  }
}

.menu-horizontal-prev::after {
  border-inline-end: 0;
  transform: translate(0, -50%) rotate(45deg);
}

.menu-horizontal-next::after {
  border-inline-start: 0;
  transform: translate(50%, -50%) rotate(315deg);
}

@include keyframes(menuDropdownShow) {
  0% {
    opacity: 0;
    transform: translateY(-.5rem);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
