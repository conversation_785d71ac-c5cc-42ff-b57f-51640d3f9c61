<?php $__env->startSection('title', 'Cards'); ?>

<?php $__env->startSection('sidebar-menu'); ?>
<!-- Cards -->
<li class="menu-item <?php echo e(request()->routeIs('cards*') ? 'active' : ''); ?>">
    <a href="<?php echo e(route('cards.index')); ?>" class="menu-link">
        <i class="menu-icon tf-icons bx bx-collection"></i>
        <div class="text-truncate" data-i18n="Basic">Cards</div>
    </a>
</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">UI Elements /</span> Cards
</h4>

<!-- Basic Cards -->
<div class="row">
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card">
            <img class="card-img-top" src="<?php echo e(asset('img/elements/2.png')); ?>" alt="Card image cap" />
            <div class="card-body">
                <h5 class="card-title">Card title</h5>
                <p class="card-text">
                    Some quick example text to build on the card title and make up the bulk of the card's content.
                </p>
                <a href="javascript:void(0)" class="btn btn-outline-primary">Go somewhere</a>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Card title</h5>
                <h6 class="card-subtitle text-muted">Support card subtitle</h6>
                <p class="card-text">
                    Some quick example text to build on the card title and make up the bulk of the card's content.
                </p>
                <a href="javascript:void(0)" class="card-link">Card link</a>
                <a href="javascript:void(0)" class="card-link">Another link</a>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Special title treatment</h5>
                <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>
                <a href="javascript:void(0)" class="btn btn-primary">Go somewhere</a>
            </div>
        </div>
    </div>
</div>

<!-- Card with Header and Footer -->
<div class="row">
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">Featured</div>
            <div class="card-body">
                <h5 class="card-title">Special title treatment</h5>
                <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>
                <a href="javascript:void(0)" class="btn btn-primary">Go somewhere</a>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card text-center">
            <div class="card-header">Featured</div>
            <div class="card-body">
                <h5 class="card-title">Special title treatment</h5>
                <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>
                <a href="javascript:void(0)" class="btn btn-primary">Go somewhere</a>
            </div>
            <div class="card-footer text-muted">2 days ago</div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\regkan\resources\views/cards/index.blade.php ENDPATH**/ ?>