// Dropdowns
// *****************************************************************

.dropdown-menu {
  box-shadow: var(--#{$prefix}dropdown-box-shadow);
  margin-block-start: 1px !important;

  text-align: start;
  > li:not(.disabled) > a:not(.dropdown-item):active,
  > li:not(.disabled) > a:not(.dropdown-item).active,
  > li.active:not(.disabled) > a:not(.dropdown-item) {
    background-color: var(--#{$prefix}dropdown-link-active-bg);
    color: var(--#{$prefix}dropdown-link-active-color);
  }
}

.btn-xs.dropdown-toggle::after {
  @include caret-down(.45em);
}

/* Split dropdowns */
.dropdown-toggle-split {
  &::after,
  .dropup &::after,
  .dropend &::after {
    margin-inline: 0;
  }
  .dropstart &::before {
    margin-inline: 0;
  }
}

/* Dropdown item line height */
.dropdown-item {
  li:not(:first-child) &,
  .dropdown-menu &:not(:first-child) {
    margin-block-start: 2px;
  }
  &.text-danger:active {
    color: var(--#{$prefix}primary) !important;
  }
}

/* Hidden dropdown toggle arrow */
.dropdown-toggle.hide-arrow,
.dropdown-toggle-hide-arrow > .dropdown-toggle {
  &::before,
  &::after {
    display: none;
  }
}

@each $breakpoint in map-keys($grid-breakpoints) {
  @include media-breakpoint-up($breakpoint) {
    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

    .dropdown-menu#{$infix}-start {
      --bs-position: start;
      &[data-bs-popper] {
        inset-inline: 0 auto;
      }
    }

    .dropdown-menu#{$infix}-end {
      --bs-position: end;
      &[data-bs-popper] {
        inset-inline: auto 0;
      }
    }
  }
}
