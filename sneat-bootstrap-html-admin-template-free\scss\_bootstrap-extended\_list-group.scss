/* List groups
******************************************************************************* */

/* List Group Mixin */
.list-group {
  --#{$prefix}list-group-timeline-bg: var(--#{$prefix}primary);
  --#{$prefix}list-group-border-color: var(--#{$prefix}border-color);
  --#{$prefix}list-group-active-border-color: var(--#{$prefix}border-color);
  --#{$prefix}list-group-action-hover-color: var(--#{$prefix}body-color);
  --#{$prefix}list-group-action-active-color: var(--#{$prefix}body-color);
  --#{$prefix}list-group-active-bg: var(--#{$prefix}primary-bg-subtle);


  .list-group-item {
    line-height: 1.375rem;
    padding-block-end: calc($list-group-item-padding-y - 1px);
  }
  &:not([class*="list-group-flush"]) .list-group-item:first-of-type {
    padding-block-start: calc($list-group-item-padding-y - 1px);
  }
  &[class*="list-group-flush"] .list-group-item:last-of-type {
    padding-block-end: $list-group-item-padding-y;
  }
  &[class*="list-group-horizontal-md"] .list-group-item {
    @include media-breakpoint-up(md) {
      padding-block-start: calc($list-group-item-padding-y - 1px);
    }
  }

  .list-group-item.active {
    h1,
    .h1,
    h2,
    .h2,
    h3,
    .h3,
    h4,
    .h4,
    h5,
    .h5,
    h6,
    .h6 {
      color: var(--#{$prefix}primary);
    }
    &,
    &:hover,
    &:focus {
      --#{$prefix}list-group-color: var(--#{$prefix}white);
    }
  }
}

// scss-docs-start list-group-modifiers

@each $state in map-keys($theme-colors) {
  .list-group-item-#{$state} {
    --#{$prefix}list-group-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}list-group-active-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}list-group-active-bg: var(--#{$prefix}#{$state}-bg-subtle);
    --#{$prefix}list-group-color: var(--#{$prefix}#{$state}-text-emphasis);
    --#{$prefix}list-group-action-hover-color: var(--#{$prefix}#{$state}-text-emphasis);
    --#{$prefix}list-group-action-active-color: var(--#{$prefix}#{$state}-text-emphasis);
  }
}

// scss-docs-end list-group-modifiers
