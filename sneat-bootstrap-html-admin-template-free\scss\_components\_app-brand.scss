/* App Brand
******************************************************************************* */
.app-brand {
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  align-items: center;


  .app-brand-text {
    opacity: 1;
    transition: opacity $menu-animation-duration ease-in-out;
  }


  .layout-menu-toggle {
    display: block;
  }

  .app-brand-img{
    display: block;
  }
  .app-brand-img-collapsed{
    display: none;
  }
}

.app-brand-link {
  display: flex;
  align-items: center;
}

/* App brand with vertical menu */
.menu-horizontal .app-brand,
.menu-horizontal .app-brand + .menu-divider {
  display: none !important;
}

@include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
  // .layout-menu-collapsed:not(.layout-menu-hover, .layout-menu-offcanvas, .layout-menu-fixed-offcanvas) .layout-menu {
  .layout-menu-collapsed:not(.layout-menu-hover) .layout-menu,
  .menu-collapsed:not(:hover) .app-brand {
    .app-brand-logo ~ .app-brand-text{
      opacity: 0;
    }
    .app-brand-img{
      display: none;
    }
    .app-brand-img-collapsed{
      display: block;
    }
  }
}

/* Within menu */
:not(.layout-menu) > .menu-vertical.menu-collapsed:not(.layout-menu):not(:hover),
.layout-menu-collapsed:not(.layout-menu-hover):not(.layout-menu-offcanvas):not(.layout-menu-fixed-offcanvas) .layout-menu {
  .app-brand {
    inline-size: $menu-collapsed-width;
  }

  .app-brand-logo,
  .app-brand-link,
  .app-brand-text {
    margin-inline: auto;
  }

  .app-brand-logo ~ .app-brand-text {
    overflow: hidden;
    opacity: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .app-brand .layout-menu-toggle {
    inset-inline-start: calc(#{$menu-collapsed-width} - 1.5rem);
    opacity: 0;
  }

  .app-brand-img {
    display: none;
  }

  .app-brand-img-collapsed {
    display: block;
  }
}
