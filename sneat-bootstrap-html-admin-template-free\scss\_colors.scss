/* Custom colors
******************************************************************************* */
:root {
  @each $color, $value in $theme-colors {
    --#{$prefix}#{$color}: #{$value};
    --#{$prefix}#{$color}-rgb: #{red($value)}, #{green($value)}, #{blue($value)};
  }

  @each $state in map-keys($theme-colors) {
    .bg-gradient-#{$state} {
      --#{$prefix}bg-gradient-color-start: color-mix(in sRGB, var(--#{$prefix}pure-black) 10%, var(--#{$prefix}#{$state}));
      --#{$prefix}bg-gradient-color-end: color-mix(in sRGB, var(--#{$prefix}white) 20%, var(--#{$prefix}#{$state}));
      background-image: linear-gradient(45deg, var(--#{$prefix}bg-gradient-color-start) 0%, var(--#{$prefix}bg-gradient-color-end) 100%) !important;
    }
  }
}
