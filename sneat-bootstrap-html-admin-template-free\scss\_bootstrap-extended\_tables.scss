/* Tables
******************************************************************************** */

/* ios fix for drodown-menu being clipped off when used in tables */
.ios .table tr > td .dropdown {
  position: relative;
}


/* Firefox fix for table head border bottom */
.table {
  > :not(caption) > * > * {
    background-clip: padding-box;
  }
  tr {
    > td {
      .dropdown {
        position: static;
      }
    }
  }

  .btn-icon,
  .btn:not([class*="btn-"]) {
    color: var(--#{$prefix}table-color);
  }

  // Table heading style
  th {
    color: var(--#{$prefix}heading-color);
    font-size: $font-size-sm;
    letter-spacing: .2px;
    text-transform: uppercase;
  }
  &:not(.table-borderless):not(.table-without-card, .dataTable) thead th {
    border-block-start-width: var(--#{$prefix}border-width);
  }

  // Removed left padding from the first column and right padding from the last column
  &.table-flush-spacing {
    thead,
    tbody {
      tr > td:first-child {
        padding-inline-start: 0;
      }
      tr > td:last-child {
        padding-inline-end: 0;
      }
    }
  }

  // Style for table inside card
  .card & {
    margin-block-end: 0;
  }

  &.table-dark,
  .table-dark {
    border-color: #{$gray-900};
    th {
      --#{$prefix}heading-color: #{$white};
    }
  }
  &.table-light,
  .table-light {
    border-color: var(--#{$prefix}border-color);
    th {
      --#{$prefix}heading-color: var(--#{$prefix}heading-color);
    }
  }
  caption {
    padding-block: $table-cell-padding-y;
    padding-inline: $table-cell-padding-x;
  }
  thead tr th {
    padding-block: $table-head-padding-y;
  }
  &.table-borderless:not(.table-sm) {
    > :not(thead) > * > * {
      padding-block: $table-cell-padding-y + .0313rem;
    }
    > thead > * > * {
      padding-block: $table-head-padding-y + .0313rem;
    }
  }
}

/* class for to remove table border bottom */
.table-border-bottom-0 {
  tr:last-child {
    td {
      border-block-end-width: 0;
    }
  }
}

/* class for to remove table border top */
.table-border-top-0 {
  tr:first-child {
    td,
    th {
      border-block-start-width: 0 !important;
    }
  }
}

// TODO: CheckInBS6 Review the `table-variants` mixin in Bootstrap 6 and update our overrides if needed to reflect any changes.

@each $state in map-keys($theme-colors) {
  .table-#{$state} {
    --#{$prefix}table-bg: rgba(var(--#{$prefix}#{$state}-rgb), .2);
    --#{$prefix}table-hover-bg: color-mix(in sRGB, var(--#{$prefix}body-bg) #{$table-hover-bg-factor-amount}, var(--#{$prefix}table-bg));
    --#{$prefix}table-border-color: color-mix(in sRGB, var(--#{$prefix}table-bg)  #{$table-border-factor-amount}, var(--#{$prefix}table-color));
    --#{$prefix}table-active-bg: color-mix(in sRGB, var(--#{$prefix}body-bg) #{$table-active-bg-factor-amount}, var(--#{$prefix}table-bg));
    @if $state == "dark" or $state == "light" {
      --#{$prefix}table-bg: var(--#{$prefix}#{$state});
      --#{$prefix}table-hover-bg: color-mix(in sRGB, var(--#{$prefix}table-color) 3.5%, var(--#{$prefix}table-bg));
      --#{$prefix}table-active-bg: color-mix(in sRGB, var(--#{$prefix}table-color) 4%, var(--#{$prefix}table-bg));
    }
    @if $state == "default" or $state == "active" {
      --#{$prefix}table-border-color: #{$gray-200};
    }
  }
}
