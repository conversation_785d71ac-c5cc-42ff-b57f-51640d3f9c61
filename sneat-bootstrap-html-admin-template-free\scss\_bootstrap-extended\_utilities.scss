// Utilities
// ******************************************************************************

// stylelint-disable indentation
// stylelint-disable @stylistic/number-leading-zero

// Utilities

$utilities: () !default;
// stylelint-disable-next-line scss/dollar-variable-default
$utilities: map-merge(
  (
    // scss-docs-start utils-vertical-align
    "align":
    (
      property: vertical-align,
      class: align,
      values: baseline top middle bottom text-bottom text-top
    ),
    // scss-docs-end utils-vertical-align
    // scss-docs-start utils-float
    "float": (
      responsive: true,
      property: float,
      values: (
        start: inline-start,
        end: inline-end,
        none: none,
      )
    ),
    // scss-docs-end utils-float
    // Object Fit utilities
    // scss-docs-start utils-object-fit
    "object-fit":
    (
      responsive: true,
      property: object-fit,
      values: (
        contain: contain,
        cover: cover,
        fill: fill,
        scale: scale-down,
        none: none
      )
    ),
    // scss-docs-end utils-object-fit
    // Opacity utilities
    // scss-docs-start utils-opacity
    "opacity": (
      property: opacity,
      values: (
        0: 0,
        25: 0.25,
        50: 0.5,
        75: 0.75,
        100: 1
      )
    ),
    // scss-docs-end utils-opacity
    // scss-docs-start utils-overflow
    "overflow": (
      property: overflow,
      values: auto hidden visible scroll
    ),
    "overflow-x": (
      property: overflow-x,
      values: auto hidden visible scroll,
    ),
    "overflow-y": (
      property: overflow-y,
      values: auto hidden visible scroll,
    ),
    // scss-docs-end utils-overflow
    // scss-docs-start utils-display
    "display": (
      responsive: true,
      print: true,
      property: display,
      class: d,
      values: inline inline-block block grid table table-row table-cell flex inline-flex none
    ),
    // scss-docs-end utils-display
    // scss-docs-start utils-shadow
    "shadow": (
      property: box-shadow,
      class: shadow,
      values: (
        null: var(--#{$prefix}box-shadow),
        sm: var(--#{$prefix}box-shadow-sm),
        lg: var(--#{$prefix}box-shadow-lg),
        xl: var(--#{$prefix}box-shadow-xl),
        none: none
      )
    ),
    // scss-docs-end utils-shadow
    // scss-docs-start utils-position
    "position": (
      property: position,
      values: static relative absolute fixed sticky
    ),
    "top": (
      property: inset-block-start,
      class: top,
      values: $position-values
    ),
    "bottom": (
      property: inset-block-end,
      class: bottom,
      values: $position-values
    ),
    "start": (
      property: inset-inline-start,
      class: start,
      values: $position-values
    ),
    "end": (
      property: inset-inline-end,
      class: end,
      values: $position-values
    ),
    "translate-middle": (
      property: transform,
      class: translate-middle,
      values: (
        null: translate(-50%, -50%),
        x: translateX(-50%),
        y: translateY(-50%),
      )
    ),
    // scss-docs-end utils-position
    // scss-docs-start utils-borders
    "border": (
      property: border,
      values: (
        null: var(--#{$prefix}border-width) var(--#{$prefix}border-style) var(--#{$prefix}border-color),
        0: 0
      )
    ),
    "border-style": (
      property: border-style,
      class: border,
      responsive: true,
      values: (
        solid: solid,
        dashed: dashed,
        none: none
      )
    ),
    "border-top": (
      property: border-block-start,
      class: border-top,
      values: (
        null: var(--#{$prefix}border-width) var(--#{$prefix}border-style) var(--#{$prefix}border-color),
        0: 0,
      )
    ),
    "border-end": (
      property: border-inline-end,
      class: border-end,
      values: (
        null: var(--#{$prefix}border-width) var(--#{$prefix}border-style) var(--#{$prefix}border-color),
        0: 0,
      )
    ),
    "border-bottom": (
      property: border-block-end,
      class: border-bottom,
      values: (
        null: var(--#{$prefix}border-width) var(--#{$prefix}border-style) var(--#{$prefix}border-color),
        0: 0,
      )
    ),
    "border-start": (
      property: border-inline-start,
      class: border-start,
      values: (
        null: var(--#{$prefix}border-width) var(--#{$prefix}border-style) var(--#{$prefix}border-color),
        0: 0,
      )
    ),
    "border-color": (
      property: border-color,
      class: border,
      local-vars: (
        "border-opacity": 1
      ),
      values: map-merge($utilities-border-colors, ("transparent": transparent )) // (C)
    ),
    //? class for this utility is border-color-subtle
    "subtle-border-color": (
      property: border-color,
      class: border,
      values: $utilities-border-subtle
    ),
    "border-width": (
      property: border-width,
      class: border,
      values: $border-widths
    ),
    "border-opacity": (
      css-var: true,
      class: border-opacity,
      values: (
        10: 0.1,
        25: 0.25,
        50: 0.5,
        75: 0.75,
        100: 1
      )
    ),
    // scss-docs-end utils-borders
    // Sizing utilities
    // scss-docs-start utils-sizing
    "width": (
      property: width,
      class: w,
      values: map-merge(
        $sizes-px,
        (
          20: 20%,
          25: 25%,
          50: 50%,
          60: 60%,
          75: 75%,
          100: 100%,
          auto: auto
        )
      )
    ),
    "max-width": (
      property: max-width,
      class: mw,
      values: (
        100: 100%
      )
    ),
    "viewport-width": (
      property: width,
      class: vw,
      values: (
        100: 100vw
      )
    ),
    "min-viewport-width": (
      property: min-width,
      class: min-vw,
      values: (
        100: 100vw
      )
    ),
    "height": (
      property: height,
      class: h,
      values: map-merge(
        $sizes-px,
        (
          25: 25%,
          50: 50%,
          75: 75%,
          100: 100%,
          auto: auto
        )
      )
    ),
    "max-height": (
      property: max-height,
      class: mh,
      values: (
        100: 100%
      )
    ),
    "viewport-height": (
      property: height,
      class: vh,
      values: (
        100: 100vh
      )
    ),
    "min-viewport-height": (
      property: min-height,
      class: min-vh,
      values: (
        100: 100vh
      )
    ),
    // scss-docs-end utils-sizing
    // Flex utilities
    // scss-docs-start utils-flex
    "flex": (
      responsive: true,
      property: flex,
      values: (
        fill: 1 1 auto
      )
    ),
    "flex-direction": (
      responsive: true,
      property: flex-direction,
      class: flex,
      values: row column row-reverse column-reverse
    ),
    "flex-grow": (
      responsive: true,
      property: flex-grow,
      class: flex,
      values: (
        grow-0: 0,
        grow-1: 1
      )
    ),
    "flex-shrink": (
      responsive: true,
      property: flex-shrink,
      class: flex,
      values: (
        shrink-0: 0,
        shrink-1: 1
      )
    ),
    "flex-wrap": (
      responsive: true,
      property: flex-wrap,
      class: flex,
      values: wrap nowrap wrap-reverse
    ),
    "justify-content": (
      responsive: true,
      property: justify-content,
      values: (
        start: flex-start,
        end: flex-end,
        center: center,
        between: space-between,
        around: space-around,
        evenly: space-evenly
      )
    ),
    "align-items": (
      responsive: true,
      property: align-items,
      values: (
        start: flex-start,
        end: flex-end,
        center: center,
        baseline: baseline,
        stretch: stretch
      )
    ),
    "align-content": (
      responsive: true,
      property: align-content,
      values: (
        start: flex-start,
        end: flex-end,
        center: center,
        between: space-between,
        around: space-around,
        stretch: stretch
      )
    ),
    "align-self": (
      responsive: true,
      property: align-self,
      values: (
        auto: auto,
        start: flex-start,
        end: flex-end,
        center: center,
        baseline: baseline,
        stretch: stretch
      )
    ),
    "order": (
      responsive: true,
      property: order,
      values: (
        first: -1,
        0: 0,
        1: 1,
        2: 2,
        3: 3,
        4: 4,
        5: 5,
        last: 6
      )
    ),
    // scss-docs-end utils-flex
    // Margin utilities
    // scss-docs-start utils-spacing
    "margin": (
      responsive: true,
      property: margin,
      class: m,
      values: map-merge(
        $spacers,
        (
          auto: auto
        )
      )
    ),
    "margin-x": (
      responsive: true,
      property: margin-inline-end margin-inline-start,
      class: mx,
      values: map-merge(
        $spacers,
        (
          auto: auto
        )
      )
    ),
    "margin-y": (
      responsive: true,
      property: margin-block-start margin-block-end,
      class: my,
      values: map-merge(
        $spacers,
        (
          auto: auto
        )
      )
    ),
    "margin-top": (
      responsive: true,
      property: margin-block-start,
      class: mt,
      values: map-merge(
        $spacers,
        (
          auto: auto
        )
      )
    ),
    "margin-end": (
      responsive: true,
      property: margin-inline-end,
      class: me,
      values: map-merge($spacers, (auto: auto))
    ),
    "margin-bottom": (
      responsive: true,
      property: margin-block-end,
      class: mb,
      values: map-merge(
        $spacers,
        (
          auto: auto
        )
      )
    ),
    "margin-start": (
      responsive: true,
      property: margin-inline-start,
      class: ms,
      values: map-merge($spacers, (auto: auto))
    ),
    // Negative margin utilities
    "negative-margin": (
      responsive: true,
      property: margin,
      class: m,
      values: $negative-spacers
    ),
    "negative-margin-x": (
      responsive: true,
      property: margin-inline-end margin-inline-start,
      class: mx,
      values: $negative-spacers
    ),
    "negative-margin-y": (
      responsive: true,
      property: margin-block-start margin-block-end,
      class: my,
      values: $negative-spacers
    ),
    "negative-margin-top": (
      responsive: true,
      property: margin-block-start,
      class: mt,
      values: $negative-spacers
    ),
    "negative-margin-end": (
      responsive: true,
      property: margin-inline-end,
      class: me,
      values: $negative-spacers
    ),
    "negative-margin-bottom": (
      responsive: true,
      property: margin-block-end,
      class: mb,
      values: $negative-spacers
    ),
    "negative-margin-start": (
      responsive: true,
      property: margin-inline-start,
      class: ms,
      values: $negative-spacers
    ),
    // Padding utilities
    "padding": (
      responsive: true,
      property: padding,
      class: p,
      values: $spacers
    ),
    "padding-x": (
      responsive: true,
      property: padding-inline-end padding-inline-start,
      class: px,
      values: $spacers
    ),
    "padding-y": (
      responsive: true,
      property: padding-block-start padding-block-end,
      class: py,
      values: $spacers
    ),
    "padding-top": (
      responsive: true,
      property: padding-block-start,
      class: pt,
      values: $spacers
    ),
    "padding-end": (
      responsive: true,
      property: padding-inline-end,
      class: pe,
      values: $spacers
    ),
    "padding-bottom": (
      responsive: true,
      property: padding-block-end,
      class: pb,
      values: $spacers
    ),
    "padding-start": (
      responsive: true,
      property: padding-inline-start,
      class: ps,
      values: $spacers
    ),
    "gap": (
      responsive: true,
      property: gap,
      class: gap,
      values: $spacers
    ),
    "row-gap": (
      responsive: true,
      property: row-gap,
      class: row-gap,
      values: $spacers
    ),
    "column-gap": (
      responsive: true,
      property: column-gap,
      class: column-gap,
      values: $spacers
    ),
    // scss-docs-end utils-spacing
    // Text
    // scss-docs-start utils-text
    "font-family": (
      property: font-family,
      class: font,
      values: (monospace: var(--#{$prefix}font-monospace))
    ),
    "font-size": (
      rfs: true,
      property: font-size,
      class: fs,
      values: map-merge(
        $font-sizes,
        (
          tiny: $tiny-font-size, // (C)
          big: $big-font-size, // (C)
          large: $large-font-size, // (C)
          xlarge: $xlarge-font-size, // (C)
          xxlarge: $xxlarge-font-size // (C)
        )
      )
    ),
    "font-style": (
      property: font-style,
      class: fst,
      values: italic normal
    ),
    "font-weight": (
      property: font-weight,
      class: fw,
      values: (
        lighter: $font-weight-lighter,
        light: $font-weight-light,
        normal: $font-weight-normal,
        medium: $font-weight-medium,
        semibold: $font-weight-semibold,
        bold: $font-weight-bold,
        extrabold: $font-weight-extrabold,
        bolder: $font-weight-bolder
      )
    ),
    "line-height": (
      property: line-height,
      class: lh,
      values: (
        1: 1,
        inherit: inherit, // (C)
        sm: $line-height-sm,
        base: $line-height-base,
        lg: $line-height-lg
      )
    ),
    "text-align": (
      responsive: true,
      property: text-align,
      class: text,
      values: (
        start: start,
        end: end,
        center: center,
      )
    ),
    "text-decoration": (
      property: text-decoration,
      values: none underline line-through
    ),
    "text-transform": (
      property: text-transform,
      class: text,
      values: none lowercase uppercase capitalize
    ),
    "white-space": (
      property: white-space,
      class: text,
      values: (
        wrap: normal,
        nowrap: nowrap
      )
    ),
    "word-wrap": (
      property: word-wrap word-break,
      class: text,
      values: (
        break: break-word
      ),
      rtl: false
    ),
    // scss-docs-end utils-text
    // scss-docs-start utils-color
    "color": (
      property: color,
      class: text,
      local-vars: (
        "text-opacity": 1
      ),
      values: map-merge(
        $utilities-text-colors,
        (
          "white": var(--#{$prefix}white),
          "body": var(--#{$prefix}body-color),
          "body-secondary": var(--#{$prefix}secondary-color),
          "body-tertiary": var(--#{$prefix}tertiary-color),
          "body-emphasis": var(--#{$prefix}emphasis-color),
          "light": $text-light, // (c)
          "lighter": $text-lighter, // (c)
          "lightest": $text-lightest, // (c)
          "heading": var(--#{$prefix}heading-color), // (c)
          "reset": inherit
        )
      )
    ),
    "text-opacity": (
      css-var: true,
      class: text-opacity,
      values: (
        25: 0.25,
        50: 0.5,
        75: 0.75,
        100: 1
      )
    ),
    "text-color": (
      property: color,
      class: text,
      values: $utilities-text-emphasis-colors
    ),
    // scss-docs-end utils-color
    // scss-docs-start utils-links
    "link-opacity": (
      css-var: true,
      class: link-opacity,
      state: hover,
      values: (
        10: 0.1,
        25: 0.25,
        50: 0.5,
        75: 0.75,
        100: 1
      )
    ),
    "link-offset": (
      property: text-underline-offset,
      class: link-offset,
      state: hover,
      values: (
        1: 0.125em,
        2: 0.25em,
        3: 0.375em
      )
    ),
    "link-underline": (
      property: text-decoration-color,
      class: link-underline,
      local-vars: (
        "link-underline-opacity": 1
      ),
      values: map-merge(
        $utilities-links-underline,
        (
          null: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-underline-opacity, 1))
        )
      )
    ),
    "link-underline-opacity": (
      css-var: true,
      class: link-underline-opacity,
      state: hover,
      values: (
        0: 0,
        10: 0.1,
        25: 0.25,
        50: 0.5,
        75: 0.75,
        100: 1
      )
    ),
    // scss-docs-end utils-links
    // scss-docs-start utils-bg-color
    "background-color": (
      property: background-color,
      class: bg,
      local-vars: (
        "bg-opacity": 1
      ),
      values: map-merge(
        $utilities-bg-colors,
        (
          "body": var(--#{$prefix}body-bg),
          "white": var(--#{$prefix}white),
          "transparent": transparent,
          "body-secondary": rgba(var(--#{$prefix}secondary-bg-rgb), var(--#{$prefix}bg-opacity)),
          "body-tertiary": rgba(var(--#{$prefix}tertiary-bg-rgb), var(--#{$prefix}bg-opacity)),
          "lighter": color-mix(in sRGB, var(--#{$prefix}base-color) 6%, var(--#{$prefix}paper-bg)), // (C)
          "lightest": color-mix(in sRGB, var(--#{$prefix}base-color) 2.5%, var(--#{$prefix}paper-bg)), // (C)
        )
      )
    ),
    "bg-opacity": (
      css-var: true,
      class: bg-opacity,
      values: (
        10: 0.1,
        25: 0.25,
        50: 0.5,
        75: 0.75,
        100: 1
      )
    ),
    "subtle-background-color": (
      property: background-color,
      class: bg,
      values: $utilities-bg-subtle
    ),
    // scss-docs-end utils-bg-color
    "gradient": (
      property: background-image,
      class: bg,
      values: (
        gradient: var(--#{$prefix}gradient)
      )
    ),
    // scss-docs-start utils-interaction
    "user-select": (
      property: user-select,
      values: all auto none
    ),
    "pointer-events": (
      property: pointer-events,
      class: pe,
      values: none auto
    ),
    // scss-docs-end utils-interaction
    // scss-docs-start utils-border-radius
    "rounded": (
      property: border-radius,
      class: rounded,
      values: (
        null: var(--#{$prefix}border-radius),
        0: 0,
        1: var(--#{$prefix}border-radius-sm),
        2: var(--#{$prefix}border-radius),
        3: var(--#{$prefix}border-radius-lg),
        4: var(--#{$prefix}border-radius-xl),
        5: var(--#{$prefix}border-radius-xxl),
        circle: 50%,
        pill: var(--#{$prefix}border-radius-pill)
      )
    ),
    "rounded-top": (
      property: border-start-start-radius border-start-end-radius,
      class: rounded-top,
      values: (
        null: var(--#{$prefix}border-radius),
        0: 0,
        1: var(--#{$prefix}border-radius-sm),
        2: var(--#{$prefix}border-radius),
        3: var(--#{$prefix}border-radius-lg),
        4: var(--#{$prefix}border-radius-xl),
        5: var(--#{$prefix}border-radius-xxl),
        circle: 50%,
        pill: var(--#{$prefix}border-radius-pill)
      )
    ),
    "rounded-end": (
      property: border-start-end-radius border-end-end-radius,
      class: rounded-end,
      values: (
        null: var(--#{$prefix}border-radius),
        0: 0,
        1: var(--#{$prefix}border-radius-sm),
        2: var(--#{$prefix}border-radius),
        3: var(--#{$prefix}border-radius-lg),
        4: var(--#{$prefix}border-radius-xl),
        5: var(--#{$prefix}border-radius-xxl),
        circle: 50%,
        pill: var(--#{$prefix}border-radius-pill)
      )
    ),
    "rounded-bottom": (
      property: border-end-end-radius border-end-start-radius,
      class: rounded-bottom,
      values: (
        null: var(--#{$prefix}border-radius),
        0: 0,
        1: var(--#{$prefix}border-radius-sm),
        2: var(--#{$prefix}border-radius),
        3: var(--#{$prefix}border-radius-lg),
        4: var(--#{$prefix}border-radius-xl),
        5: var(--#{$prefix}border-radius-xxl),
        circle: 50%,
        pill: var(--#{$prefix}border-radius-pill)
      )
    ),
    "rounded-start": (
      property: border-end-start-radius border-start-start-radius,
      class: rounded-start,
      values: (
        null: var(--#{$prefix}border-radius),
        0: 0,
        1: var(--#{$prefix}border-radius-sm),
        2: var(--#{$prefix}border-radius),
        3: var(--#{$prefix}border-radius-lg),
        4: var(--#{$prefix}border-radius-xl),
        5: var(--#{$prefix}border-radius-xxl),
        circle: 50%,
        pill: var(--#{$prefix}border-radius-pill)
      )
    ),
    "rounded-start-top": (
      property: border-start-start-radius,
      class: rounded-start-top,
      values: (
        null: $border-radius
      )
    ),
    "rounded-start-bottom": (
      property: border-end-start-radius,
      class: rounded-start-bottom,
      values: (
        null: $border-radius
      )
    ),
    "rounded-end-top": (
      property: border-start-end-radius,
      class: rounded-end-top,
      values: (
        null: $border-radius
      )
    ),
    "rounded-end-bottom": (
      property: border-end-end-radius,
      class: rounded-end-bottom,
      values: (
        null: $border-radius
      )
    ),
    // scss-docs-end utils-border-radius
    // scss-docs-start utils-visibility
    "visibility": (
      property: visibility,
      class: null,
      values: (
        visible: visible,
        invisible: hidden
      )
    ),
    // scss-docs-end utils-visibility
    // scss-docs-start utils-zindex
    "z-index": (
      property: z-index,
      class: z,
      values: $zindex-levels
    ),
    // scss-docs-end utils-zindex
    // Custom Utilities
    // *******************************************************************************
    // scss-docs-start utils-rotate
    "rotate": (
      property: transform,
      class: rotate,
      values: (
        0: var(--#{$prefix}rotate-0, rotate(0deg)),
        90: var(--#{$prefix}rotate-90, rotate(90deg)),
        180: var(--#{$prefix}rotate-180, rotate(180deg)),
        270: var(--#{$prefix}rotate-270, rotate(270deg)),
        n90: var(--#{$prefix}rotate-n90, rotate(-90deg)),
        n180: var(--#{$prefix}rotate-n180, rotate(-180deg)),
        n270: var(--#{$prefix}rotate-n270, rotate(-270deg))
      )
    ),
    // scss-docs-end utils-rotate
    // scss-docs-start utils-flex-basis
    "cursor": (
      property: cursor,
      class: cursor,
      values: pointer move grab
    ),
    // scss-docs-end utils-flex-basis
  ),
  $utilities
);
// stylelint-enable @stylistic/number-leading-zero

// Borders
// *******************************************************************************

// Bordered rows
.row-bordered {
  overflow: hidden;

  > .col,
  > [class^="col-"],
  > [class*=" col-"],
  > [class^="col "],
  > [class*=" col "],
  > [class$=" col"],
  > [class="col"] {
    position: relative;
    padding-block-start: 1px;

    &::before {
      position: absolute;
      display: block;
      block-size: 0;
      border-block-start: 1px solid $bordered-row-border-color;
      content: "";
      inset-block-end: -1px;
      inset-inline: 0;
    }

    &::after {
      position: absolute;
      display: block;
      border-inline-start: 1px solid $bordered-row-border-color;
      content: "";
      inline-size: 0;
      inset-block: 0;
      inset-inline-start: -1px;
    }
  }

  &.row-border-light {
    > .col,
    > [class^="col-"],
    > [class*=" col-"],
    > [class^="col "],
    > [class*=" col "],
    > [class$=" col"],
    > [class="col"] {
      &::before,
      &::after {
        border-color: $gray-100;
      }
    }
  }
}

.text-body-secondary[href] {
  &:hover,
  &:focus {
    color: $body-secondary-color-hover !important;
  }
}

a.bg-dark {
  &:hover,
  &:focus {
    background-color: $gray-900 !important;
  }
}

a.bg-light {
  &:hover,
  &:focus {
    background-color: $gray-200 !important;
  }
}

a.bg-lighter {
  &:hover,
  &:focus {
    background-color: $gray-100 !important;
  }
}

a.bg-lightest {
  &:hover,
  &:focus {
    background-color: $gray-60 !important;
  }
}

.text-light {
  &[href] {
    &:hover,
    &:focus {
      color: $body-secondary-color-hover !important;
    }
  }
}

.text-lighter {
  &[href] {
    &:hover,
    &:focus {
      color: $body-secondary-color-hover !important;
    }
  }
}

.text-lightest {
  &[href] {
    &:hover,
    &:focus {
      color: $body-secondary-color-hover !important;
    }
  }
}

.text-paper {
  color: var(--#{$prefix}paper-bg) !important;

  &[href] {
    &:hover,
    &:focus {
      color: var(--#{$prefix}primary) !important;
    }
  }
}

// text-body hover color
.text-body,
.text-heading {
  &[href]:hover {
    color: var(--#{$prefix}primary) !important;
  }
}

// Invertible colors

.invert-text-white {
  color: light-dark(var(--#{$prefix}white), $body-bg) !important;
}
.invert-text-white[href]:hover {
  &:hover,
  &:focus {
    color: light-dark(var(--#{$prefix}white), $body-bg) !important;
  }
}

.invert-text-dark {
  color: light-dark($black, var(--#{$prefix}white)) !important;
}
.invert-text-dark[href]:hover {
  &:hover,
  &:focus {
    color: light-dark($black, var(--#{$prefix}white)) !important;
  }
}

.invert-bg-white {
  background-color: light-dark(var(--#{$prefix}white), $body-bg) !important;
}
a.invert-bg-white {
  &:hover,
  &:focus {
    background-color: light-dark(var(--#{$prefix}white), $body-bg) !important;
  }
}

.invert-bg-dark {
  background-color: light-dark(var(--#{$prefix}gray-900), var(--#{$prefix}white)) !important;
}
a.invert-bg-dark {
  &:hover,
  &:focus {
    background-color: light-dark(var(--#{$prefix}gray-900), var(--#{$prefix}white)) !important;
  }
}

.invert-border-dark {
  border-color: light-dark(var(--#{$prefix}dark), var(--#{$prefix}white)) !important;
}

.invert-border-white {
  border-color: light-dark(var(--#{$prefix}white), $body-bg) !important;
}

// Misc
// *******************************************************************************

// Layout containers
.container-p-x {
  padding-inline: $container-padding-x-sm !important;

  @include media-breakpoint-up(lg) {
    padding-inline: $container-padding-x !important;
  }
}

.container-m-nx {
  margin-inline: -$container-padding-x-sm !important;

  @include media-breakpoint-up(lg) {
    margin-inline: -$container-padding-x !important;
  }
}

.container-p-y {
  &:not([class^="pt-"]):not([class*=" pt-"]) {
    padding-block-start: $container-padding-y !important;
  }

  &:not([class^="pb-"]):not([class*=" pb-"]) {
    padding-block-end: $container-padding-y !important;
  }
}

.container-m-ny {
  &:not([class^="mt-"]):not([class*=" mt-"]) {
    margin-block-start: -$container-padding-y !important;
  }

  &:not([class^="mb-"]):not([class*=" mb-"]) {
    margin-block-end: -$container-padding-y !important;
  }
}

.icon-base {
  @include icon-base();
  line-height: 1;
  vertical-align: middle;
}

// icon sizes
@each $key, $value in $icon-sizes {
  .icon-#{$key} {
    &,
    &::before {
      @include icon-base($value !important);
    }
  }
}

.icon-xs{
  &,
  &::before {
    @include icon-base(var(--#{$prefix}icon-size-xs) !important);
  }
}

.icon-sm{
  &,
  &::before {
    @include icon-base(var(--#{$prefix}icon-size-sm) !important);
  }
}

.icon-md{
  &,
  &::before {
    @include icon-base(var(--#{$prefix}icon-size-md) !important);
  }
}

.icon-lg{
  &,
  &::before {
    @include icon-base(var(--#{$prefix}icon-size-lg) !important);
  }
}

.icon-xl{
  &,
  &::before {
    @include icon-base(var(--#{$prefix}icon-size-xl) !important);
  }
}


// Table cell
.cell-fit {
  inline-size: .1%;
  white-space: nowrap;
}
