// Form control
// *******************************************************************************

.form-control {
  --#{$prefix}input-border-color: #{$input-border-color};
  --#{$prefix}input-disabled-border-color: #{$input-disabled-border-color};
  border-color: var(--#{$prefix}input-border-color);

  // ? Form control (all size) padding calc due to border increase on focus
  padding-block: calc($input-padding-y - $input-border-width);
  padding-inline: calc($input-padding-x - $input-border-width);

  // form input placeholder animation
  &::placeholder,
  &:focus::placeholder {
    @include transition(all ease .2s);
  }

  /* border color on hover state when element not in focus or disabled */
  &:hover {
    &:not(:focus):not(:disabled) {
      border-color: $input-hover-border-color;
    }
  }
  &:disabled {
    border-color: var(--#{$prefix}input-disabled-border-color);
  }

  /*
  ! FIX: wizard-ex input type number placeholder align issue */
  &[type="number"] {
    .input-group & {
      line-height: 1.375rem;
      min-block-size: 2.375rem;
    }
    .input-group-lg & {
      line-height: 1.5rem;
      min-block-size: 3rem;
    }
    .input-group-sm & {
      min-block-size: 1.875rem;
    }
  }

  &:not([readonly]) {
    &:focus::placeholder {
      transform: translateX(5px);
    }
  }

  &:focus {
    border-width: $input-focus-border-width;
    padding-block: calc($input-padding-y - $input-focus-border-width);
    padding-inline: calc($input-padding-x - $input-focus-border-width);
    &::file-selector-button {
      box-shadow: $input-border-width 0 0  $input-focus-border-color;
    }
  }
  &.form-control-lg {
    padding-block: calc($input-padding-y-lg - $input-border-width);
    padding-inline: calc($input-padding-x-lg - $input-border-width);
    &:focus {
      padding-block: calc($input-padding-y-lg - $input-focus-border-width);
      padding-inline: calc($input-padding-x-lg - $input-focus-border-width);
    }
    &::file-selector-button {
      margin-block: (-$input-padding-y-lg - .0625rem);
      padding-block: calc($input-padding-y-lg + .0625rem);
    }
  }
  &.form-control-sm {
    padding-block: calc($input-padding-y-sm - $input-border-width);
    padding-inline: calc($input-padding-x-sm - $input-border-width);
    &:focus {
      padding-block: calc($input-padding-y-sm - $input-focus-border-width);
      padding-inline: calc($input-padding-x-sm - $input-focus-border-width);
    }
    &::file-selector-button {
      margin-block: (-$input-padding-y-sm - .0625rem);
      padding-block: calc($input-padding-y-sm + .0625rem);
    }
  }
}
